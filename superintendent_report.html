<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Operations Status Report - Northwest School Division</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: #00CDFE;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            overflow: hidden;
        }

        .header {
            background: #00ADF2;
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
        }

        .header h1 {
            font-size: 2.2em;
            margin-bottom: 5px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
        }

        .header .subtitle {
            font-size: 1.2em;
            opacity: 0.95;
            margin-bottom: 20px;
        }

        .header .date {
            font-size: 1em;
            opacity: 0.9;
            background: rgba(255,255,255,0.2);
            display: inline-block;
            padding: 8px 20px;
            border-radius: 20px;
        }

        .executive-summary {
            background: #F7F7F7;
            padding: 40px;
            margin: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }

        .executive-summary h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.8em;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .executive-summary h2::before {
            content: '📊';
            margin-right: 10px;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
            justify-items: center;
        }

        .metric-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .metric-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }

        .metric-value {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .metric-label {
            color: #7f8c8d;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .completed { color: #27ae60; }
        .in-progress { color: #f39c12; }
        .incomplete { color: #e74c3c; }

        .sow-section {
            margin: 30px;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .sow-header {
            background: #00ADF2;
            color: white;
            padding: 25px;
            position: relative;
        }

        .sow-header h2 {
            font-size: 1.5em;
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 12px;
        }

        .sow-header .progress-bar {
            margin-top: 15px;
            background: rgba(255,255,255,0.3);
            height: 10px;
            border-radius: 5px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #2ecc71, #27ae60);
            border-radius: 5px;
            transition: width 1s ease;
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .sow-stats {
            display: flex;
            justify-content: space-around;
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 1.8em;
            font-weight: bold;
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.9em;
            margin-top: 5px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        thead {
            background: #f8f9fa;
        }

        th, td {
            padding: 15px;
            text-align: left;
            font-weight: 600;
            color: #495057;
            border-bottom: 2px solid #dee2e6;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        th:nth-child(1), td:nth-child(1) { width: 20%; }
        th:nth-child(2), td:nth-child(2) { width: 60%; }
        th:nth-child(3), td:nth-child(3) { width: 20%; }

        td {
            padding: 12px 15px;
            border-bottom: 1px solid #e9ecef;
        }

        tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-completed {
            background: #d4edda;
            color: #155724;
        }

        .status-in-progress {
            background: #fff3cd;
            color: #856404;
        }

        .status-incomplete {
            background: #f8d7da;
            color: #721c24;
        }

        .key-achievements {
            margin: 30px;
            padding: 30px;
            background: #E8EDF4;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .key-achievements h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.8em;
            display: flex;
            align-items: center;
        }

        .key-achievements h2::before {
            content: '🏆';
            margin-right: 10px;
        }

        .achievement-list {
            list-style: none;
            padding: 0;
        }

        .achievement-list li {
            background: white;
            margin-bottom: 10px;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
            display: flex;
            align-items: center;
        }

        .achievement-list li::before {
            content: '✓';
            color: #28a745;
            font-weight: bold;
            margin-right: 10px;
            font-size: 1.2em;
        }

        .footer {
            background: #2c3e50;
            color: white;
            padding: 30px;
            text-align: center;
        }

        .footer .prepared-by {
            font-size: 1.1em;
            margin-bottom: 10px;
        }

        .footer .contact {
            opacity: 0.8;
            font-size: 0.9em;
        }

        @media print {
            body {
                background: white;
                padding: 0;
            }
            .container {
                box-shadow: none;
                border-radius: 0;
            }
        }

        .icon {
            display: inline-block;
            margin-right: 8px;
        }

        .sow-percent {
            font-size: 0.95em;
            background: rgba(255,255,255,0.15);
            padding: 6px 10px;
            border-radius: 12px;
            color: white;
            font-weight: 700;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Security Operations Status Report</h1>
            <div class="subtitle">Northwest School Division - IT Security & Compliance</div>
            <h3 style="margin-top:12px; font-size:1.1em; opacity:0.95;">Arctic Wolf</h3>
        </div>

        <div class="executive-summary">
            <h2>Executive Summary</h2>
            <p style="font-size: 1.05em; line-height: 1.6; color: #34495e; margin: 0 auto; max-width: 900px;">
                Successfully strengthened Northwest School Division's security posture through completion of 17 critical initiatives
                (73.9% completion rate), including full Active Directory hardening, UPS monitoring configuration, and elimination
                of legacy SSL/TLS vulnerabilities. Zero items pending - all work actively progressing.
            </p>
            <div style="height:18px"></div>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value completed">17</div>
                    <div class="metric-label">Completed</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value in-progress">6</div>
                    <div class="metric-label">In Progress</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value incomplete">0</div>
                    <div class="metric-label">Pending</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" style="color: #3498db;">23</div>
                    <div class="metric-label">Total Tickets</div>
                </div>
            </div>
        </div>

        <div class="sow-section">
            <div class="sow-header">
                <h2><span class="icon">🗂️</span> Strategic plan tickets 2024/2025 <span class="sow-percent">85.7% Complete</span></h2>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 85.714%;"></div>
                </div>
            </div>
            <div class="sow-stats">
                <div class="stat-item">
                    <div class="stat-value completed">6</div>
                    <div class="stat-label">Completed</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value in-progress">1</div>
                    <div class="stat-label">In Progress</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value incomplete">0</div>
                    <div class="stat-label">Pending</div>
                </div>
            </div>
            <table>
                <thead>
                    <tr>
                        <th>Ticket ID</th>
                        <th>Description</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>SR057311</strong></td>
                        <td>Set APC UPS to email <NAME_EMAIL> for each UPS; Updated notification settings to send all Critical events once. Email server settings primary dns ************, secondary dns ************, outgoing mail configuration from address smtprelay_o365!nwsd.ca, SMTP server nwsd-ca.mail.protection.outlook.com, port 25, <NAME_EMAIL></td>
                        <td><span class="status-badge status-completed">Completed</span></td>
                    </tr>
                    <tr>
                        <td><strong>SR057287</strong></td>
                        <td>ASR Report 2023-2024</td>
                        <td><span class="status-badge status-completed">Completed</span></td>
                    </tr>
                    <tr>
                        <td><strong>SR057252</strong></td>
                        <td>Set APC UPS to email <NAME_EMAIL>; Adjust APC email notification server settings as required (currently using **************)</td>
                        <td><span class="status-badge status-completed">Completed</span></td>
                    </tr>
                    <tr>
                        <td><strong>SR055383</strong></td>
                        <td>Conduct an audit of our Certificate Authority (CA) templates to enhance security and reduce certificate misuse.</td>
                        <td><span class="status-badge status-in-progress">In Progress</span></td>
                    </tr>
                    <tr>
                        <td><strong>SR054145</strong></td>
                        <td>Look at setting up the NICs for the IOSafe 1GbE LAN Port x2 with Link Aggregation / Failover support</td>
                        <td><span class="status-badge status-completed">Completed</span></td>
                    </tr>
                    <tr>
                        <td><strong>SR054008</strong></td>
                        <td>Start reviewing Intune policies and ensure existing policies will work with Windows 11 in preparation for the Windows 11 staff device rollout.</td>
                        <td><span class="status-badge status-completed">Completed</span></td>
                    </tr>
                    <tr>
                        <td><strong>SR051117</strong></td>
                        <td>Look at setup of MFA for Atrieve</td>
                        <td><span class="status-badge status-completed">Completed</span></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="sow-section">
            <div class="sow-header">
                <h2 style="margin:0; display:flex; align-items:center; justify-content:center;"><span class="icon">🐺</span> Arctic Wolf</h2>
                <div class="progress-bar" style="display:none;">
                    <div class="progress-fill" style="width: 0%;"></div>
                </div>
            </div>
            <div style="padding:20px 30px; background:#fff; border-top:1px solid #e9ecef;">
                <p style="margin:0; font-size:1em; color:#34495e;">
                    For Arctic Wolf work, I will re-engage the phishing e-mails on September 1st 2025. I will continue with the Arctic Wolf SPiDRs/journey.
                </p>
            </div>
        </div>



        <div class="sow-section">
            <div class="sow-header">
                <h2><span class="icon">🔍</span> Vulnerability Assessment & Penetration Testing SOW <span class="sow-percent">100% Complete</span></h2>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 100%;"></div>
                </div>
            </div>
            <div class="sow-stats">
                <div class="stat-item">
                    <div class="stat-value completed">1</div>
                    <div class="stat-label">Completed</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value in-progress">0</div>
                    <div class="stat-label">In Progress</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value incomplete">0</div>
                    <div class="stat-label">Pending</div>
                </div>
            </div>
            <table>
                <thead>
                    <tr>
                        <th>Ticket ID</th>
                        <th>Description</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>SR061320</strong></td>
                        <td>Disable support for SSL v2, SSL v3, TLS 1.0 and 1.1</td>
                        <td><span class="status-badge status-completed">Completed</span></td>
                    </tr>
                </tbody>
            </table>
        </div>


        <div class="sow-section">
            <div class="sow-header">
                <h2><span class="icon">🔐</span> Active Directory Security Assessment & Account Audit SOW <span class="sow-percent">71.4% Complete</span></h2>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 71.4%;"></div>
                </div>
            </div>
            <div class="sow-stats">
                <div class="stat-item">
                    <div class="stat-value completed">10</div>
                    <div class="stat-label">Completed</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value in-progress">4</div>
                    <div class="stat-label">In Progress</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value incomplete">0</div>
                    <div class="stat-label">Pending</div>
                </div>
            </div>
            <table>
                <thead>
                    <tr>
                        <th>Ticket ID</th>
                        <th>Description</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td></td>
                        <td>Review accounts with “password never expires” configured, particularly for students using Clever Badges and RADIUS authentication for devices.</td>
                        <td><span class="status-badge status-in-progress" style="background: #fff3cd; color: #856404;">In Progress</span></td>
                    </tr>
                    <tr>
                        <td></td>
                        <td>Set complex, 25+ character passwords for accounts used in RADIUS authentication that don’t require user input.</td>
                        <td><span class="status-badge status-in-progress" style="background: #fff3cd; color: #856404;">In Progress</span></td>
                    </tr>
                    <tr>
                        <td></td>
                        <td>Review and potentially remove users from privileged groups, particularly focusing on the SCAdmin access path identified. (Tony is looking into this group to see if its privileged</td>
                        <td><span class="status-badge status-in-progress" style="background: #fff3cd; color: #856404;">In Progress</span></td>
                    </tr>
                    <tr>
                        <td></td>
                        <td>Want to look at bumping up staff password length to 12 characters</td>
                        <td><span class="status-badge status-in-progress" style="background: #fff3cd; color: #856404;">In Progress</span></td>
                    </tr>
                    <tr>
                        <td></td>
                        <td>Change the administrator account password to a complex, 25+ character password.</td>
                        <td><span class="status-badge status-completed">Completed</span></td>
                    </tr>
                    <tr>
                        <td></td>
                        <td>Remove or delete the IT.installer account if it’s no longer needed.</td>
                        <td><span class="status-badge status-completed">Completed</span></td>
                    </tr>
                    <tr>
                        <td></td>
                        <td>Enable the “Do not store LAN Manager hash value on next password change” policy in Active Directory.</td>
                        <td><span class="status-badge status-completed">Completed</span></td>
                    </tr>
                    <tr>
                        <td></td>
                        <td>Address user accounts with blank passwords, particularly the signage.Jonas account and one other mentioned account.</td>
                        <td><span class="status-badge status-completed">Completed</span></td>
                    </tr>
                    <tr>
                        <td></td>
                        <td>Review and change passwords for accounts that haven’t been changed in over 10 years, especially privileged accounts.</td>
                        <td><span class="status-badge status-completed">Completed</span></td>
                    </tr>
                    <tr>
                        <td></td>
                        <td>Enable SMB signing to prevent potential exploits during penetration testing.</td>
                        <td><span class="status-badge status-completed">Completed</span></td>
                    </tr>
                    <tr>
                        <td></td>
                        <td>Implement Local Administrator Password Solution (LAPS) for Active Directory-joined systems.</td>
                        <td><span class="status-badge status-completed">Completed</span></td>
                    </tr>
                    <tr>
                        <td></td>
                        <td>Default domain controller policy set to disable print spooler</td>
                        <td><span class="status-badge status-completed">Completed</span></td>
                    </tr>
                    <tr>
                        <td></td>
                        <td>Look into Entra ID Password protection</td>
                        <td><span class="status-badge status-completed">Completed</span></td>
                    </tr>
                    <tr>
                        <td></td>
                        <td>Change user accounts that do not require a password to require a password.</td>
                        <td><span class="status-badge status-completed">Completed</span></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="sow-section">
          <div class="sow-header">
            <h2>Business Continuity Planning SOW</h2>
          </div>
          <div class="sow-stats">
                <div class="stat-item">
                    <div class="stat-value completed">0</div>
                    <div class="stat-label">Completed</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value in-progress">1</div>
                    <div class="stat-label">In Progress</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value incomplete">0</div>
                    <div class="stat-label">Pending</div>
                </div>
            </div>
            <table>
                <thead>
                    <tr>
                        <th>Ticket ID</th>
                        <th>Description</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>SR056011
</strong></td>
                        <td>Last task was to do another application of our choosing which last time we talked, was going to be Veeam</td>
                        <td><span class="status-badge status-in-progress">In Progress</span></td>
                    </tr>
                </tbody>
            </table>
        </div>
<div class="sow-section">
  <div class="sow-header">
    <h2>Justifications for Incomplete Work</h2>
  </div>
  <div style="padding:20px 30px; background:#fff; border-top:1px solid #e9ecef;">
    <p style="margin:0; font-size:1em; color:#34495e;">
      The remaining work was planned for completion over the summer, but I had to cover co-workers’ sick and vacation days. During this time, Jared was managing a large Access Point rollout that required additional support, so I made myself available to assist where I could. In addition, several tickets not assigned to me related to the IBM penetration test were stalled, so I took ownership and completed the necessary research. Below are some tickets not complete that are primarily in Jared and Keegan's domain.
    </p>
    <ul style="margin-top:12px; color:#34495e;">
      <li>Want to look at bumping up staff password length to 12 characters - Will need plan from Keegan</li>
      <li>Set complex, 25+ character passwords for accounts used in RADIUS authentication that don’t require user input Service requests - This will require a radius redesign, that is Jared and Keagan's domain.</li>
    </ul>
  </div>
</div>
        <div class="footer">
            <div class="prepared-by">Prepared by: Ryley Dyck</div>
            <div class="contact">IT Security Analyst | Northwest School Division</div>
        </div>
    </div>
    </body>
    </html>